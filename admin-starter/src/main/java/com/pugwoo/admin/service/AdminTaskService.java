package com.pugwoo.admin.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.dbhelper.model.PageData;

public interface AdminTaskService {

    /**
     * 重跑指定的任务日志记录
     * @param logId 任务日志ID
     * @return 重跑结果，成功返回新的日志记录ID
     */
    ResultBean<Long> rerunTask(Long logId);

    /**
     * 运行指定的任务
     * @param taskId 任务ID
     * @param args 任务参数，JSON格式的数组字符串
     * @return 运行结果，成功返回新的日志记录ID
     */
    ResultBean<Long> runTask(Long taskId, String args);

    /**
     * 通过主键获得数据
     */
    AdminTaskDO getById(Integer id);
    
    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<AdminTaskDO> getPage(int page, int pageSize);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(AdminTaskDO adminTaskDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    // 以下是log部分

    /**
     * 通过主键获得数据
     */
    AdminTaskLogDO getLogById(Long id);

    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     * @param taskId 任务ID，可选参数，用于过滤特定任务的日志
     */
    PageData<AdminTaskLogDO> getLogPage(int page, int pageSize, Long taskId);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdateLog(AdminTaskLogDO adminTaskLogDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteLogById(Long id);

}