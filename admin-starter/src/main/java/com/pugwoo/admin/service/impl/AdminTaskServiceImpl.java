package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.SpringContext;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.enums.AdminTaskStatusEnum;
import com.pugwoo.admin.enums.AdminTaskTriggerTypeEnum;
import com.pugwoo.admin.service.AdminTaskService;
import com.pugwoo.admin.utils.ClassUtils;
import com.pugwoo.admin.web.tasklog.TaskLogContext;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;

@Service
@Slf4j
public class AdminTaskServiceImpl implements AdminTaskService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    private static ExecutorService pool = ThreadPoolUtils.createThreadPool(
            10, 60, 30, "rerun-task");

    @Override
    public AdminTaskDO getById(Integer id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(AdminTaskDO.class, id);
    }

    @Override
    public PageData<AdminTaskDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(AdminTaskDO.class, page, pageSize);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(AdminTaskDO adminTaskDO) {
        if(adminTaskDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(adminTaskDO);
        return rows > 0 ? ResultBean.ok(adminTaskDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        AdminTaskDO adminTaskDO = new AdminTaskDO();
        adminTaskDO.setId(id);
        return dbHelper.delete(adminTaskDO) > 0;
    }

    // log部分

    @Override
    public AdminTaskLogDO getLogById(Long id) {
        if(id == null) {
            return null;
        }
        return dbHelper.getByKey(AdminTaskLogDO.class, id);
    }

    @Override
    public PageData<AdminTaskLogDO> getLogPage(int page, int pageSize, Long taskId) {
        if (taskId == null) {
            return dbHelper.getPage(AdminTaskLogDO.class, page, pageSize);
        } else {
            return dbHelper.getPage(AdminTaskLogDO.class, page, pageSize,
                "where task_id = ? order by id desc", taskId);
        }
    }

    @Override
    public ResultBean<Long> insertOrUpdateLog(AdminTaskLogDO adminTaskLogDO) {
        if(adminTaskLogDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(adminTaskLogDO);
        return rows > 0 ? ResultBean.ok(adminTaskLogDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteLogById(Long id) {
        if(id == null) {
            return false;
        }

        AdminTaskLogDO adminTaskLogDO = new AdminTaskLogDO();
        adminTaskLogDO.setId(id);
        return dbHelper.delete(adminTaskLogDO) > 0;
    }

    @Override
    public ResultBean<Long> rerunTask(Long logId) {
        if (logId == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数logId");
        }

        // 1. 获取原始任务日志记录
        AdminTaskLogDO originalLog = dbHelper.getByKey(AdminTaskLogDO.class, logId);
        if (originalLog == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务日志记录不存在");
        }

        // 2. 如果任务禁用，则不允许重跑
        AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, originalLog.getTaskId());
        if (Boolean.TRUE.equals(task.getCtrlDisabled())) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务被禁用，不允许重跑");
        }

        try {
            // 3. 创建新的任务日志记录
            AdminTaskLogDO newLog = createNewLogForRerun(originalLog);
            dbHelper.insert(newLog);

            // 4. 执行任务
            pool.submit(() -> executeTask(originalLog, newLog));

            return ResultBean.ok(newLog.getId());
        } catch (Exception e) {
            log.error("重跑任务失败", e);
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "重跑任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean<Long> runTask(Long taskId, String args) {
        if (taskId == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数taskId");
        }

        // 1. 获取任务信息
        AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskId);
        if (task == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务不存在");
        }

        // 2. 如果任务禁用，则不允许运行
        if (Boolean.TRUE.equals(task.getCtrlDisabled())) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务被禁用，不允许运行");
        }

        try {
            // 3. 创建新的任务日志记录
            AdminTaskLogDO newLog = createNewLogForRun(task, args);
            dbHelper.insert(newLog);

            // 4. 执行任务
            pool.submit(() -> executeTaskFromTask(task, newLog, args));

            return ResultBean.ok(newLog.getId());
        } catch (Exception e) {
            log.error("运行任务失败", e);
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "运行任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建重跑的新日志记录
     */
    private AdminTaskLogDO createNewLogForRerun(AdminTaskLogDO originalLog) {
        AdminTaskLogDO newLog = new AdminTaskLogDO();
        newLog.setTaskId(originalLog.getTaskId());
        newLog.setTaskName(originalLog.getTaskName());
        newLog.setTaskCode(originalLog.getTaskCode());
        newLog.setClassName(originalLog.getClassName());
        newLog.setMethodName(originalLog.getMethodName());
        newLog.setArgs(originalLog.getArgs());
        newLog.setCronExpression(originalLog.getCronExpression());
        newLog.setFixRateMs(originalLog.getFixRateMs());
        newLog.setTimeoutSecond(originalLog.getTimeoutSecond());
        newLog.setStatus(AdminTaskStatusEnum.NEW.getCode());
        newLog.setCostMs(0);
        newLog.setErrorMsg("");

        // 设置触发类型为页面触发
        newLog.setTriggerType(AdminTaskTriggerTypeEnum.MANUAL.getCode());

        try {
            newLog.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            newLog.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }

        return newLog;
    }

    /**
     * 创建运行任务的新日志记录
     */
    private AdminTaskLogDO createNewLogForRun(AdminTaskDO task, String args) {
        AdminTaskLogDO newLog = new AdminTaskLogDO();
        newLog.setTaskId(task.getId());
        newLog.setTaskName(task.getTaskName());
        newLog.setTaskCode(task.getTaskCode());
        newLog.setClassName(task.getClassName());
        newLog.setMethodName(task.getMethodName());
        newLog.setArgs(args);
        newLog.setCronExpression(task.getCronExpression());
        newLog.setFixRateMs(task.getFixRateMs());
        newLog.setTimeoutSecond(task.getTimeoutSecond());
        newLog.setStatus(AdminTaskStatusEnum.NEW.getCode());
        newLog.setCostMs(0);
        newLog.setErrorMsg("");

        // 设置触发类型为页面触发
        newLog.setTriggerType(AdminTaskTriggerTypeEnum.MANUAL.getCode());

        try {
            newLog.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            newLog.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }

        return newLog;
    }

    /**
     * 执行任务
     */
    private void executeTask(AdminTaskLogDO originalLog, AdminTaskLogDO newLog) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 设置任务状态为RUNNING
            newLog.setStatus(AdminTaskStatusEnum.RUNNING.getCode());
            dbHelper.update(newLog);
            
            // 设置任务的isRunning状态为true
            AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, originalLog.getTaskId());
            if (task != null) {
                task.setIsRunning(true);
                dbHelper.update(task);
            }

            // 2. 获取类和方法
            Class<?> clazz = Class.forName(originalLog.getClassName());
            Object bean = SpringContext.context.getBean(clazz);

            // 3. 解析方法签名和参数
            String methodSignature = originalLog.getMethodName();
            Method method = findMethodBySignature(clazz, methodSignature);
            if (method == null) {
                throw new RuntimeException("找不到方法: " + methodSignature);
            }

            // 4. 解析参数
            Object[] args = parseArgs(originalLog.getArgs(), method.getParameterTypes());

            // 5. 执行方法
            TaskLogContext.setIsTriggerByManual(true);
            method.invoke(bean, args);
            TaskLogContext.setIsTriggerByManual(false);

            // 6. 更新成功状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.SUCCESS.getCode());
            newLog.setCostMs((int) costMs);
            dbHelper.update(newLog);

            // 7. 更新任务统计信息
            updateTaskSuccessInfo(originalLog.getTaskId());

        } catch (Exception e) {
            // 更新失败状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.FAIL.getCode());
            newLog.setCostMs((int) costMs);

            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            String errorMsg = writer.toString();
            if (errorMsg.length() > 65000) {
                errorMsg = errorMsg.substring(0, 65000);
            }
            newLog.setErrorMsg(errorMsg);
            dbHelper.update(newLog);

            // 更新任务失败信息
            updateTaskFailInfo(originalLog.getTaskId());

            throw new RuntimeException("任务执行失败", e);
        }
    }

    /**
     * 从任务定义执行任务
     */
    private void executeTaskFromTask(AdminTaskDO task, AdminTaskLogDO newLog, String args) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 设置任务状态为RUNNING
            newLog.setStatus(AdminTaskStatusEnum.RUNNING.getCode());
            dbHelper.update(newLog);
            
            // 设置任务的isRunning状态为true
            task.setIsRunning(true);
            dbHelper.update(task);

            // 2. 获取类和方法
            Class<?> clazz = Class.forName(task.getClassName());
            Object bean = SpringContext.context.getBean(clazz);

            // 3. 解析方法签名和参数
            String methodSignature = task.getMethodName();
            Method method = findMethodBySignature(clazz, methodSignature);
            if (method == null) {
                throw new RuntimeException("找不到方法: " + methodSignature);
            }

            // 4. 解析参数
            Object[] methodArgs = parseArgs(args, method.getParameterTypes());

            // 5. 执行方法
            TaskLogContext.setIsTriggerByManual(true);
            method.invoke(bean, methodArgs);
            TaskLogContext.setIsTriggerByManual(false);

            // 6. 更新成功状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.SUCCESS.getCode());
            newLog.setCostMs((int) costMs);
            dbHelper.update(newLog);

            // 7. 更新任务统计信息
            updateTaskSuccessInfo(task.getId());

        } catch (Exception e) {
            // 更新失败状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.FAIL.getCode());
            newLog.setCostMs((int) costMs);

            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            String errorMsg = writer.toString();
            if (errorMsg.length() > 65000) {
                errorMsg = errorMsg.substring(0, 65000);
            }
            newLog.setErrorMsg(errorMsg);
            dbHelper.update(newLog);

            // 更新任务失败信息
            updateTaskFailInfo(task.getId());

            throw new RuntimeException("任务执行失败", e);
        }
    }

    /**
     * 根据方法签名查找方法
     */
    private Method findMethodBySignature(Class<?> clazz, String methodSignature) {
        for (Method method : clazz.getDeclaredMethods()) {
            if (ClassUtils.getMethodSignature(method).equals(methodSignature)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 解析参数
     */
    private Object[] parseArgs(String argsJson, Class<?>[] parameterTypes) {
        if (StringTools.isBlank(argsJson) || "null".equals(argsJson)) {
            return new Object[0];
        }

        try {
            Object[] rawArgs = JSON.parse(argsJson, Object[].class);
            if (rawArgs == null) {
                return new Object[0];
            }

            Object[] typedArgs = new Object[rawArgs.length];
            for (int i = 0; i < rawArgs.length && i < parameterTypes.length; i++) {
                if (rawArgs[i] == null) {
                    typedArgs[i] = null;
                } else {
                    // 简单的类型转换，可能需要根据实际情况扩展
                    typedArgs[i] = convertArgument(rawArgs[i], parameterTypes[i]);
                }
            }
            return typedArgs;
        } catch (Exception e) {
            log.error("解析参数失败: " + argsJson, e);
            return new Object[0];
        }
    }

    /**
     * 参数类型转换
     */
    private Object convertArgument(Object arg, Class<?> targetType) {
        if (arg == null) {
            return null;
        }

        if (targetType.isAssignableFrom(arg.getClass())) {
            return arg;
        }

        // 基本类型转换
        if (targetType == String.class) {
            return arg.toString();
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.valueOf(arg.toString());
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.valueOf(arg.toString());
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.valueOf(arg.toString());
        } else if (targetType == Double.class || targetType == double.class) {
            return Double.valueOf(arg.toString());
        } else if (targetType == Float.class || targetType == float.class) {
            return Float.valueOf(arg.toString());
        }

        // 对于复杂对象，尝试JSON转换
        try {
            String json = JSON.toJson(arg);
            return JSON.parse(json, targetType);
        } catch (Exception e) {
            log.warn("参数类型转换失败，使用原始值: " + arg.getClass() + " -> " + targetType, e);
            return arg;
        }
    }

    /**
     * 更新任务成功信息
     */
    private void updateTaskSuccessInfo(Long taskId) {
        if (taskId == null) {
            return;
        }

        try {
            AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskId);
            if (task != null) {
                task.setIsLastSuccess(true);
                task.setIsRunning(false); // 任务完成，设置运行状态为false
                task.setLastTime(LocalDateTime.now());
                task.setCountSuccess((task.getCountSuccess() == null ? 0 : task.getCountSuccess()) + 1);
                dbHelper.update(task);
            }
        } catch (Exception e) {
            log.error("更新任务成功信息失败", e);
        }
    }

    /**
     * 更新任务失败信息
     */
    private void updateTaskFailInfo(Long taskId) {
        if (taskId == null) {
            return;
        }

        try {
            AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskId);
            if (task != null) {
                task.setIsLastSuccess(false);
                task.setIsRunning(false); // 任务完成，设置运行状态为false
                task.setLastErrorTime(LocalDateTime.now());
                task.setCountError((task.getCountError() == null ? 0 : task.getCountError()) + 1);
                dbHelper.update(task);
            }
        } catch (Exception e) {
            log.error("更新任务失败信息失败", e);
        }
    }

}