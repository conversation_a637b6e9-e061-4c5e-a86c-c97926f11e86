package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.service.AdminTaskService;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(value = "/admin_task")
public class AdminTaskController {

    @Autowired
    private AdminTaskService adminTaskService;

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize) {
        PageData<AdminTaskDO> pageData = adminTaskService.getPage(page, pageSize);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(AdminTaskDO adminTaskDO) {
        WebCheckUtils.assertNotNull(adminTaskDO, "缺少修改的对象参数");

        // 检查note字段长度
        if (adminTaskDO.getNote() != null && adminTaskDO.getNote().length() > 1000) {
            return (WebJsonBean<Long>) WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, "任务备注长度不能超过1000个字符");
        }

        ResultBean<Long> result = adminTaskService.insertOrUpdate(adminTaskDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(adminTaskService.deleteById(id));
    }

    // task log相关接口

    @GetMapping("log_get_page")
    public WebJsonBean<Map<String, Object>> logGetPage(int page, int pageSize, Long taskId) {
        PageData<AdminTaskLogDO> pageData = adminTaskService.getLogPage(page, pageSize, taskId);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }

    @PostMapping("log_add_or_update")
    public WebJsonBean<Long> logAddOrUpdate(AdminTaskLogDO adminTaskLogDO) {
        WebCheckUtils.assertNotNull(adminTaskLogDO, "缺少修改的对象参数");
        // TODO check parameters

        ResultBean<Long> result = adminTaskService.insertOrUpdateLog(adminTaskLogDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }

    @PostMapping("log_delete")
    public WebJsonBean<Boolean> logDelete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(adminTaskService.deleteLogById(id));
    }

    @PostMapping("log_rerun")
    public WebJsonBean<Long> logRerun(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        ResultBean<Long> result = adminTaskService.rerunTask(id);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }

    @PostMapping("run_task")
    public WebJsonBean<Long> runTask(Long taskId, String args) {
        WebCheckUtils.assertNotNull(taskId, "缺少参数taskId");
        ResultBean<Long> result = adminTaskService.runTask(taskId, args);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
}
